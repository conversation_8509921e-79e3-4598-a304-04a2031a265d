{"name": "handbook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bun run --bun vite --host", "dev:api": "bun run --bun backend/server.ts", "dev:full": "concurrently \"bun run dev:api\" \"bun run dev\"", "build": "bun run --bun vite build", "build:dev": "bun run --bun vite build --mode development", "build:analyze": "bun run --bun vite build --mode analyze", "lint": "bun run --bun eslint .", "preview": "bun run --bun vite preview", "perf": "bun run build && bun run preview --host", "db:migrate": "bun run frontend/lib/db/migrate.ts", "db:migrate:sqlserver": "sqlcmd -S localhost -d doaxvv_handbook -i backend/migrations/001_enhanced_schema_sqlserver.sql", "db:migrate:data": "bun run backend/scripts/migrate-data.ts", "db:seed": "bun run backend/seed.ts", "test": "bun run backend/tests/api.test.ts", "test:perf": "bun run backend/tests/performance.test.ts", "test:migration": "bun run backend/scripts/test-migration.ts", "start": "bun run dev", "start:prod": "bun run build && concurrently \"bun run backend/server.ts\" \"bun run preview --host\""}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-bubble-menu": "^2.14.0", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-document": "^2.14.0", "@tiptap/extension-dropcursor": "^2.14.0", "@tiptap/extension-focus": "^2.14.0", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/extension-gapcursor": "^2.14.0", "@tiptap/extension-hard-break": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-history": "^2.14.0", "@tiptap/extension-horizontal-rule": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-mention": "^2.14.0", "@tiptap/extension-paragraph": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/mssql": "^9.1.7", "@types/sqlite3": "^5.1.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "framer-motion": "^10.18.0", "helmet": "^8.1.0", "joi": "^17.13.3", "lowlight": "^2.9.0", "lucide-react": "^0.292.0", "morgan": "^1.10.0", "mssql": "^11.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.63", "zustand": "^4.5.7"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.0.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^4.5.14"}}