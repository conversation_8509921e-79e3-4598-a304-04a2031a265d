#!/usr/bin/env bun
/**
 * Simple Database Migration Script for SQLite
 * 
 * This script creates the basic tables without foreign keys first
 */

import { config } from 'dotenv';
config();

import { open, Database } from 'sqlite';
import sqlite3 from 'sqlite3';
import { join } from 'path';
import logger from '../config/logger';

async function runSimpleMigration(): Promise<void> {
  let db: Database<sqlite3.Database, sqlite3.Statement> | null = null;
  
  try {
    logger.info('🚀 Starting simple SQLite database migration...');
    
    // Connect to database
    const dbPath = join(__dirname, '../data/doaxvv_handbook.db');
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    logger.info('✅ Connected to SQLite database');
    
    // Enable foreign keys
    await db.exec('PRAGMA foreign_keys = ON');
    
    // Create tables one by one
    logger.info('📝 Creating tables...');
    
    // Characters table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS characters (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        name_jp TEXT,
        name_en TEXT,
        name_zh TEXT,
        rarity TEXT CHECK (rarity IN ('SSR', 'SR', 'R', 'N')) DEFAULT 'R',
        birthday DATE,
        height INTEGER CHECK (height > 0 AND height < 300),
        bust INTEGER CHECK (bust > 0 AND bust < 200),
        waist INTEGER CHECK (waist > 0 AND waist < 200),
        hip INTEGER CHECK (hip > 0 AND hip < 200),
        hobby TEXT,
        favorite_food TEXT,
        description TEXT,
        avatar_image TEXT,
        background_image TEXT,
        voice_actor TEXT,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Skills table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS skills (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('offensive', 'defensive', 'support', 'special', 'technical', 'balanced', 'appeal')),
        category TEXT DEFAULT 'general',
        description TEXT,
        icon TEXT,
        max_level INTEGER DEFAULT 1 CHECK (max_level > 0 AND max_level <= 10),
        effect_type TEXT,
        effect_value REAL,
        cooldown INTEGER DEFAULT 0,
        duration INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Swimsuits table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS swimsuits (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        character_id TEXT NOT NULL,
        rarity TEXT NOT NULL CHECK (rarity IN ('UR', 'SSR', 'SR', 'R', 'N')),
        type TEXT DEFAULT 'swimsuit' CHECK (type IN ('swimsuit', 'outfit', 'dress', 'casual')),
        pow INTEGER NOT NULL CHECK (pow >= 0),
        tec INTEGER NOT NULL CHECK (tec >= 0),
        stm INTEGER NOT NULL CHECK (stm >= 0),
        apl INTEGER NOT NULL CHECK (apl >= 0),
        release_date DATE NOT NULL,
        reappear_date DATE,
        image TEXT,
        thumbnail_image TEXT,
        gacha_type TEXT DEFAULT 'standard',
        is_limited INTEGER DEFAULT 0,
        is_collab INTEGER DEFAULT 0,
        collab_series TEXT,
        trend_type TEXT,
        cost INTEGER DEFAULT 0,
        upgrade_cost INTEGER DEFAULT 0,
        max_level INTEGER DEFAULT 80 CHECK (max_level > 0),
        description TEXT,
        obtain_method TEXT DEFAULT 'gacha',
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Shop Items table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS shop_items (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL CHECK (type IN ('swimsuit', 'accessory', 'decoration', 'currency', 'booster')),
        category TEXT NOT NULL,
        section TEXT NOT NULL CHECK (section IN ('owner', 'event', 'venus', 'vip')),
        price INTEGER NOT NULL CHECK (price >= 0),
        currency TEXT NOT NULL CHECK (currency IN ('coins', 'gems', 'tickets')),
        rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
        image TEXT,
        in_stock INTEGER DEFAULT 1,
        is_new INTEGER DEFAULT 0,
        discount INTEGER DEFAULT 0 CHECK (discount >= 0 AND discount <= 100),
        limited_time INTEGER DEFAULT 0,
        featured INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Memories table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL CHECK (type IN ('photo', 'video')),
        date DATE NOT NULL,
        characters TEXT,
        tags TEXT,
        thumbnail TEXT,
        favorite INTEGER DEFAULT 0,
        unlocked INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Events table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS events (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('festival', 'gacha', 'ranking', 'mission', 'collab')),
        description TEXT,
        start_date DATETIME NOT NULL,
        end_date DATETIME NOT NULL,
        image TEXT,
        is_active INTEGER DEFAULT 0,
        rewards TEXT,
        requirements TEXT,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now')),
        CHECK (end_date > start_date)
      )
    `);
    
    // Bromides table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS bromides (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect')),
        rarity TEXT NOT NULL CHECK (rarity IN ('N', 'R', 'SR', 'SSR', 'UR')),
        description TEXT,
        character_id TEXT,
        effects TEXT,
        source TEXT,
        image TEXT,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Document Categories table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS document_categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        color TEXT DEFAULT '#6B7280',
        description TEXT,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Documents table
    await db.exec(`
      CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT,
        author TEXT NOT NULL,
        is_published INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime('now')),
        updated_at DATETIME DEFAULT (datetime('now'))
      )
    `);
    
    // Create indexes
    logger.info('📊 Creating indexes...');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_characters_name ON characters(name)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_characters_rarity ON characters(rarity)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_swimsuits_character_id ON swimsuits(character_id)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_swimsuits_rarity ON swimsuits(rarity)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_shop_items_section ON shop_items(section)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_shop_items_featured ON shop_items(featured)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_memories_type ON memories(type)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_memories_favorite ON memories(favorite)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_events_type ON events(type)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_events_active ON events(is_active)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_bromides_type ON bromides(type)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_bromides_rarity ON bromides(rarity)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_documents_published ON documents(is_published)');
    
    // Insert initial data
    logger.info('📝 Inserting initial data...');
    
    // Document categories
    await db.exec(`
      INSERT OR IGNORE INTO document_categories (id, name, color, description) VALUES
      ('tutorial', 'Tutorial', '#3B82F6', 'Step-by-step guides and tutorials'),
      ('reference', 'Reference', '#10B981', 'Reference materials and documentation'),
      ('gameplay', 'Gameplay', '#F59E0B', 'Gameplay strategies and tips'),
      ('update', 'Updates', '#EF4444', 'Game updates and patch notes'),
      ('community', 'Community', '#8B5CF6', 'Community resources and guides')
    `);
    
    logger.info('✅ Database migration completed successfully!');
    
    // Verify migration by checking if tables exist
    const tables = await db.all(`
      SELECT name 
      FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    
    logger.info('📊 Created tables:');
    tables.forEach(table => logger.info(`  - ${table.name}`));
    
    logger.info(`🎉 Migration complete! Created ${tables.length} tables.`);
    
  } catch (error: any) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (db) {
      await db.close();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run migration if this script is executed directly
if (import.meta.main) {
  runSimpleMigration()
    .then(() => {
      logger.info('✨ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { runSimpleMigration };
