#!/usr/bin/env bun
/**
 * Database Migration Script for SQLite
 * 
 * This script runs the comprehensive schema migration for DOAXVV Handbook using SQLite
 */

import { config } from 'dotenv';
config();

import { open, Database } from 'sqlite';
import sqlite3 from 'sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import logger from '../config/logger';

async function runMigration(): Promise<void> {
  let db: Database<sqlite3.Database, sqlite3.Statement> | null = null;
  
  try {
    logger.info('🚀 Starting SQLite database migration...');
    
    // Connect to database
    const dbPath = join(__dirname, '../data/doaxvv_handbook.db');
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    logger.info('✅ Connected to SQLite database');
    
    // Enable foreign keys
    await db.exec('PRAGMA foreign_keys = ON');
    
    // Read migration file
    const migrationPath = join(__dirname, '../migrations/001_comprehensive_schema_sqlite.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    logger.info('📄 Migration file loaded');
    
    // Execute the entire migration as a single transaction
    await db.exec('BEGIN TRANSACTION');
    
    try {
      // Split SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      logger.info(`📝 Found ${statements.length} SQL statements to execute`);
      
      // Execute each statement
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement.trim()) {
          try {
            logger.info(`⚡ Executing statement ${i + 1}/${statements.length}`);
            await db.exec(statement);
          } catch (error: any) {
            // Log the error but continue with other statements for non-critical errors
            if (error.message.includes('already exists') || error.message.includes('duplicate')) {
              logger.warn(`⚠️  Statement ${i + 1} skipped (already exists): ${error.message}`);
            } else {
              logger.error(`❌ Statement ${i + 1} failed: ${error.message}`);
              logger.debug(`Statement content: ${statement.substring(0, 100)}...`);
              throw error; // Re-throw for critical errors
            }
          }
        }
      }
      
      await db.exec('COMMIT');
      logger.info('✅ Database migration completed successfully!');
      
    } catch (error) {
      await db.exec('ROLLBACK');
      throw error;
    }
    
    // Verify migration by checking if tables exist
    const tablesQuery = `
      SELECT name 
      FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `;
    
    const tables = await db.all(tablesQuery);
    
    logger.info('📊 Created tables:');
    tables.forEach(table => logger.info(`  - ${table.name}`));
    
    logger.info(`🎉 Migration complete! Created ${tables.length} tables.`);
    
    // Test basic functionality
    logger.info('🧪 Testing database functionality...');
    
    // Test character insertion
    await db.run(`
      INSERT OR IGNORE INTO characters (id, name, rarity, created_at, updated_at) 
      VALUES (?, ?, ?, datetime('now'), datetime('now'))
    `, ['test-char-1', 'Test Character', 'SSR']);
    
    // Test character retrieval
    const testChar = await db.get('SELECT * FROM characters WHERE id = ?', ['test-char-1']);
    if (testChar) {
      logger.info('✅ Database functionality test passed');
      
      // Clean up test data
      await db.run('DELETE FROM characters WHERE id = ?', ['test-char-1']);
    } else {
      logger.warn('⚠️  Database functionality test failed');
    }
    
  } catch (error: any) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (db) {
      await db.close();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run migration if this script is executed directly
if (import.meta.main) {
  runMigration()
    .then(() => {
      logger.info('✨ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { runMigration };
