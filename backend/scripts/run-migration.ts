#!/usr/bin/env bun
/**
 * Database Migration Script for SQL Server
 *
 * This script runs the comprehensive schema migration for DOAXVV Handbook
 */

import { config } from 'dotenv';
config();

import * as sql from 'mssql';
import { readFileSync } from 'fs';
import { join } from 'path';
import logger from '../config/logger';

// Database configuration
const dbConfig = {
  server: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '1433'),
  database: process.env.DB_NAME || 'doaxvv_handbook',
  user: process.env.DB_USER || 'doaxvv_user',
  password: process.env.DB_PASSWORD || 'doaxvv_password',
  options: {
    encrypt: process.env.NODE_ENV === 'production',
    trustServerCertificate: process.env.NODE_ENV !== 'production',
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 60000,
  connectionTimeout: 15000,
};

async function runMigration(): Promise<void> {
  let pool: sql.ConnectionPool | null = null;

  try {
    logger.info('🚀 Starting database migration...');

    // Connect to database
    pool = new sql.ConnectionPool(dbConfig);
    await pool.connect();
    logger.info('✅ Connected to SQL Server database');

    // Read migration file
    const migrationPath = join(__dirname, '../migrations/001_comprehensive_schema_sqlserver.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    logger.info('📄 Migration file loaded');

    // Split SQL into individual statements (handle GO statements)
    const statements = migrationSQL
      .split(/\r?\n/)
      .join('\n')
      .split(/\nGO\n|\ngo\n/i)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    logger.info(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          logger.info(`⚡ Executing statement ${i + 1}/${statements.length}`);
          await pool.request().query(statement);
        } catch (error: any) {
          // Log the error but continue with other statements
          logger.warn(`⚠️  Statement ${i + 1} failed: ${error.message}`);
          logger.debug(`Statement content: ${statement.substring(0, 100)}...`);
        }
      }
    }

    logger.info('✅ Database migration completed successfully!');

    // Verify migration by checking if tables exist
    const tablesQuery = `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `;

    const result = await pool.request().query(tablesQuery);
    const tables = result.recordset.map(row => row.TABLE_NAME);

    logger.info('📊 Created tables:');
    tables.forEach(table => logger.info(`  - ${table}`));

    logger.info(`🎉 Migration complete! Created ${tables.length} tables.`);

  } catch (error: any) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (pool) {
      await pool.close();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run migration if this script is executed directly
if (import.meta.main) {
  runMigration()
    .then(() => {
      logger.info('✨ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { runMigration };
