#!/usr/bin/env bun
/**
 * Database Migration Script for PostgreSQL
 *
 * This script runs the comprehensive schema migration for DOAXVV Handbook
 */

import { config } from 'dotenv';
config();

import { Pool } from 'pg';
import { readFileSync } from 'fs';
import { join } from 'path';
import logger from '../config/logger';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'doaxvv_handbook',
  user: process.env.DB_USER || 'doaxvv_user',
  password: process.env.DB_PASSWORD || 'doaxvv_password',
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 15000,
};

async function runMigration(): Promise<void> {
  let pool: Pool | null = null;

  try {
    logger.info('🚀 Starting database migration...');

    // Connect to database
    pool = new Pool(dbConfig);
    logger.info('✅ Connected to PostgreSQL database');

    // Read migration file
    const migrationPath = join(__dirname, '../migrations/001_comprehensive_schema_postgres.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    logger.info('📄 Migration file loaded');

    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    logger.info(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          logger.info(`⚡ Executing statement ${i + 1}/${statements.length}`);
          await pool.query(statement);
        } catch (error: any) {
          // Log the error but continue with other statements
          logger.warn(`⚠️  Statement ${i + 1} failed: ${error.message}`);
          logger.debug(`Statement content: ${statement.substring(0, 100)}...`);
        }
      }
    }

    logger.info('✅ Database migration completed successfully!');

    // Verify migration by checking if tables exist
    const tablesQuery = `
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;

    const result = await pool.query(tablesQuery);
    const tables = result.rows.map(row => row.table_name);

    logger.info('📊 Created tables:');
    tables.forEach(table => logger.info(`  - ${table}`));

    logger.info(`🎉 Migration complete! Created ${tables.length} tables.`);

  } catch (error: any) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (pool) {
      await pool.end();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run migration if this script is executed directly
if (import.meta.main) {
  runMigration()
    .then(() => {
      logger.info('✨ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { runMigration };
