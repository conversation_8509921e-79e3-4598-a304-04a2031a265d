#!/usr/bin/env bun
/**
 * Database Reset Script for PostgreSQL
 * 
 * This script drops all tables and recreates the schema
 */

import { config } from 'dotenv';
config();

import { Pool } from 'pg';
import logger from '../config/logger';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'doaxvv_handbook',
  user: process.env.DB_USER || 'doaxvv_user',
  password: process.env.DB_PASSWORD || 'doaxvv_password',
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 15000,
};

async function resetDatabase(): Promise<void> {
  let pool: Pool | null = null;
  
  try {
    logger.info('🚀 Starting database reset...');
    
    // Connect to database
    pool = new Pool(dbConfig);
    logger.info('✅ Connected to PostgreSQL database');
    
    // Drop all tables
    logger.info('🗑️  Dropping existing tables...');
    const dropTables = [
      'DROP TABLE IF EXISTS activity_logs CASCADE',
      'DROP TABLE IF EXISTS user_statistics CASCADE',
      'DROP TABLE IF EXISTS user_settings CASCADE',
      'DROP TABLE IF EXISTS shop_items CASCADE',
      'DROP TABLE IF EXISTS memories CASCADE',
      'DROP TABLE IF EXISTS document_categories CASCADE',
      'DROP TABLE IF EXISTS documents CASCADE',
      'DROP TABLE IF EXISTS bromides CASCADE',
      'DROP TABLE IF EXISTS events CASCADE',
      'DROP TABLE IF EXISTS venus_boards CASCADE',
      'DROP TABLE IF EXISTS girl_accessories CASCADE',
      'DROP TABLE IF EXISTS swimsuit_skills CASCADE',
      'DROP TABLE IF EXISTS accessories CASCADE',
      'DROP TABLE IF EXISTS girls CASCADE',
      'DROP TABLE IF EXISTS swimsuits CASCADE',
      'DROP TABLE IF EXISTS skills CASCADE',
      'DROP TABLE IF EXISTS characters CASCADE',
      'DROP TABLE IF EXISTS migrations CASCADE',
    ];
    
    for (const dropSQL of dropTables) {
      await pool.query(dropSQL);
    }
    
    // Enable extensions
    logger.info('🔧 Enabling PostgreSQL extensions...');
    await pool.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    await pool.query('CREATE EXTENSION IF NOT EXISTS "pg_trgm"');
    
    // Create tables
    logger.info('🏗️  Creating tables...');
    
    // Characters table
    await pool.query(`
      CREATE TABLE characters (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        name_jp VARCHAR(255),
        name_en VARCHAR(255),
        name_zh VARCHAR(255),
        rarity VARCHAR(10) CHECK (rarity IN ('SSR', 'SR', 'R', 'N')) DEFAULT 'R',
        birthday DATE,
        height INTEGER CHECK (height > 0 AND height < 300),
        bust INTEGER CHECK (bust > 0 AND bust < 200),
        waist INTEGER CHECK (waist > 0 AND waist < 200),
        hip INTEGER CHECK (hip > 0 AND hip < 200),
        hobby TEXT,
        favorite_food TEXT,
        description TEXT,
        avatar_image VARCHAR(500),
        background_image VARCHAR(500),
        voice_actor VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Skills table
    await pool.query(`
      CREATE TABLE skills (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL CHECK (type IN ('offensive', 'defensive', 'support', 'special', 'technical', 'balanced', 'appeal')),
        category VARCHAR(100) DEFAULT 'general',
        description TEXT,
        icon VARCHAR(255),
        max_level INTEGER DEFAULT 1 CHECK (max_level > 0 AND max_level <= 10),
        effect_type VARCHAR(100),
        effect_value DECIMAL(5,2),
        cooldown INTEGER DEFAULT 0,
        duration INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Swimsuits table
    await pool.query(`
      CREATE TABLE swimsuits (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        character_id VARCHAR(255) NOT NULL,
        rarity VARCHAR(10) NOT NULL CHECK (rarity IN ('UR', 'SSR', 'SR', 'R', 'N')),
        type VARCHAR(50) DEFAULT 'swimsuit' CHECK (type IN ('swimsuit', 'outfit', 'dress', 'casual')),
        pow INTEGER NOT NULL CHECK (pow >= 0),
        tec INTEGER NOT NULL CHECK (tec >= 0),
        stm INTEGER NOT NULL CHECK (stm >= 0),
        apl INTEGER NOT NULL CHECK (apl >= 0),
        release_date DATE NOT NULL,
        reappear_date DATE,
        image VARCHAR(500),
        thumbnail_image VARCHAR(500),
        gacha_type VARCHAR(50) DEFAULT 'standard',
        is_limited BOOLEAN DEFAULT FALSE,
        is_collab BOOLEAN DEFAULT FALSE,
        collab_series VARCHAR(255),
        trend_type VARCHAR(50),
        cost INTEGER DEFAULT 0,
        upgrade_cost INTEGER DEFAULT 0,
        max_level INTEGER DEFAULT 80 CHECK (max_level > 0),
        description TEXT,
        obtain_method VARCHAR(100) DEFAULT 'gacha',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      )
    `);
    
    // Girls table
    await pool.query(`
      CREATE TABLE girls (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        character_id VARCHAR(255),
        type VARCHAR(10) NOT NULL CHECK (type IN ('pow', 'tec', 'stm', 'apl')),
        level INTEGER NOT NULL CHECK (level >= 1 AND level <= 100),
        experience INTEGER DEFAULT 0 CHECK (experience >= 0),
        pow INTEGER NOT NULL CHECK (pow >= 0),
        tec INTEGER NOT NULL CHECK (tec >= 0),
        stm INTEGER NOT NULL CHECK (stm >= 0),
        apl INTEGER NOT NULL CHECK (apl >= 0),
        max_pow INTEGER NOT NULL CHECK (max_pow >= pow),
        max_tec INTEGER NOT NULL CHECK (max_tec >= tec),
        max_stm INTEGER NOT NULL CHECK (max_stm >= stm),
        max_apl INTEGER NOT NULL CHECK (max_apl >= apl),
        potential_pow INTEGER DEFAULT 0 CHECK (potential_pow >= 0),
        potential_tec INTEGER DEFAULT 0 CHECK (potential_tec >= 0),
        potential_stm INTEGER DEFAULT 0 CHECK (potential_stm >= 0),
        potential_apl INTEGER DEFAULT 0 CHECK (potential_apl >= 0),
        birthday DATE NOT NULL,
        swimsuit_id VARCHAR(255),
        is_awakened BOOLEAN DEFAULT FALSE,
        awakening_level INTEGER DEFAULT 0 CHECK (awakening_level >= 0 AND awakening_level <= 5),
        friendship_level INTEGER DEFAULT 1 CHECK (friendship_level >= 1 AND friendship_level <= 30),
        mood VARCHAR(20) DEFAULT 'normal' CHECK (mood IN ('excellent', 'good', 'normal', 'tired', 'exhausted')),
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id),
        FOREIGN KEY (swimsuit_id) REFERENCES swimsuits(id)
      )
    `);
    
    // Accessories table
    await pool.query(`
      CREATE TABLE accessories (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(20) NOT NULL CHECK (type IN ('head', 'face', 'hand', 'body', 'back', 'special')),
        rarity VARCHAR(10) DEFAULT 'R' CHECK (rarity IN ('UR', 'SSR', 'SR', 'R', 'N')),
        skill_id VARCHAR(255) NOT NULL,
        pow INTEGER DEFAULT 0 CHECK (pow >= 0),
        tec INTEGER DEFAULT 0 CHECK (tec >= 0),
        stm INTEGER DEFAULT 0 CHECK (stm >= 0),
        apl INTEGER DEFAULT 0 CHECK (apl >= 0),
        level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 40),
        max_level INTEGER DEFAULT 40 CHECK (max_level >= level),
        image VARCHAR(500),
        obtain_method VARCHAR(100) DEFAULT 'shop',
        cost INTEGER DEFAULT 0,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
      )
    `);
    
    // Events table
    await pool.query(`
      CREATE TABLE events (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL CHECK (type IN ('festival', 'gacha', 'ranking', 'mission', 'collab')),
        description TEXT,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        image VARCHAR(500),
        is_active BOOLEAN DEFAULT FALSE,
        rewards JSONB,
        requirements JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CHECK (end_date > start_date)
      )
    `);

    // Bromides table
    await pool.query(`
      CREATE TABLE bromides (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL CHECK (type IN ('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect')),
        rarity VARCHAR(10) NOT NULL CHECK (rarity IN ('N', 'R', 'SR', 'SSR', 'UR')),
        description TEXT,
        character_id VARCHAR(255),
        effects JSONB,
        source VARCHAR(255),
        image VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE SET NULL
      )
    `);

    // Document Categories table
    await pool.query(`
      CREATE TABLE document_categories (
        id VARCHAR(100) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        color VARCHAR(50) DEFAULT '#6B7280',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Documents table
    await pool.query(`
      CREATE TABLE documents (
        id VARCHAR(255) PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        content TEXT NOT NULL,
        category VARCHAR(100) NOT NULL,
        tags TEXT[],
        author VARCHAR(255) NOT NULL,
        is_published BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category) REFERENCES document_categories(id)
      )
    `);

    // Memories table
    await pool.query(`
      CREATE TABLE memories (
        id VARCHAR(255) PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        description TEXT,
        type VARCHAR(20) NOT NULL CHECK (type IN ('photo', 'video')),
        date DATE NOT NULL,
        characters TEXT[],
        tags TEXT[],
        thumbnail VARCHAR(500),
        favorite BOOLEAN DEFAULT FALSE,
        unlocked BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Shop Items table
    await pool.query(`
      CREATE TABLE shop_items (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(50) NOT NULL CHECK (type IN ('swimsuit', 'accessory', 'decoration', 'currency', 'booster')),
        category VARCHAR(100) NOT NULL,
        section VARCHAR(20) NOT NULL CHECK (section IN ('owner', 'event', 'venus', 'vip')),
        price INTEGER NOT NULL CHECK (price >= 0),
        currency VARCHAR(20) NOT NULL CHECK (currency IN ('coins', 'gems', 'tickets')),
        rarity VARCHAR(20) NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
        image VARCHAR(500),
        in_stock BOOLEAN DEFAULT TRUE,
        is_new BOOLEAN DEFAULT FALSE,
        discount INTEGER DEFAULT 0 CHECK (discount >= 0 AND discount <= 100),
        limited_time BOOLEAN DEFAULT FALSE,
        featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // User Settings table
    await pool.query(`
      CREATE TABLE user_settings (
        key VARCHAR(255) PRIMARY KEY,
        value TEXT NOT NULL,
        data_type VARCHAR(50) DEFAULT 'string' CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
        category VARCHAR(100) DEFAULT 'general',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // User Statistics table
    await pool.query(`
      CREATE TABLE user_statistics (
        id SERIAL PRIMARY KEY,
        stat_name VARCHAR(255) NOT NULL,
        stat_value BIGINT NOT NULL DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (stat_name)
      )
    `);

    // Activity Log table
    await pool.query(`
      CREATE TABLE activity_logs (
        id SERIAL PRIMARY KEY,
        action_type VARCHAR(100) NOT NULL,
        entity_type VARCHAR(100) NOT NULL,
        entity_id VARCHAR(255) NOT NULL,
        old_values JSONB,
        new_values JSONB,
        ip_address INET,
        user_agent TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Migrations table
    await pool.query(`
      CREATE TABLE migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes
    logger.info('📊 Creating indexes...');
    await pool.query('CREATE INDEX idx_characters_name ON characters(name)');
    await pool.query('CREATE INDEX idx_characters_rarity ON characters(rarity)');
    await pool.query('CREATE INDEX idx_swimsuits_character_id ON swimsuits(character_id)');
    await pool.query('CREATE INDEX idx_swimsuits_rarity ON swimsuits(rarity)');
    await pool.query('CREATE INDEX idx_swimsuits_release_date ON swimsuits(release_date)');
    await pool.query('CREATE INDEX idx_girls_character_id ON girls(character_id)');
    await pool.query('CREATE INDEX idx_girls_level ON girls(level)');
    await pool.query('CREATE INDEX idx_accessories_skill_id ON accessories(skill_id)');
    await pool.query('CREATE INDEX idx_accessories_type ON accessories(type)');
    await pool.query('CREATE INDEX idx_events_type ON events(type)');
    await pool.query('CREATE INDEX idx_events_active ON events(is_active)');
    await pool.query('CREATE INDEX idx_bromides_type ON bromides(type)');
    await pool.query('CREATE INDEX idx_bromides_rarity ON bromides(rarity)');
    await pool.query('CREATE INDEX idx_documents_category ON documents(category)');
    await pool.query('CREATE INDEX idx_documents_published ON documents(is_published)');
    await pool.query('CREATE INDEX idx_memories_type ON memories(type)');
    await pool.query('CREATE INDEX idx_memories_favorite ON memories(favorite)');
    await pool.query('CREATE INDEX idx_shop_items_type ON shop_items(type)');
    await pool.query('CREATE INDEX idx_shop_items_section ON shop_items(section)');
    await pool.query('CREATE INDEX idx_shop_items_featured ON shop_items(featured)');

    // Insert initial data
    logger.info('📝 Inserting initial data...');

    // Document categories
    await pool.query(`
      INSERT INTO document_categories (id, name, color, description) VALUES
      ('tutorial', 'Tutorial', '#3B82F6', 'Step-by-step guides and tutorials'),
      ('reference', 'Reference', '#10B981', 'Reference materials and documentation'),
      ('gameplay', 'Gameplay', '#F59E0B', 'Gameplay strategies and tips'),
      ('update', 'Updates', '#EF4444', 'Game updates and patch notes'),
      ('community', 'Community', '#8B5CF6', 'Community resources and guides')
    `);

    // User statistics
    await pool.query(`
      INSERT INTO user_statistics (stat_name, stat_value) VALUES
      ('total_characters', 0),
      ('total_swimsuits', 0),
      ('total_skills', 0),
      ('total_girls', 0),
      ('total_accessories', 0),
      ('total_events', 0),
      ('total_bromides', 0),
      ('total_documents', 0),
      ('total_memories', 0),
      ('total_shop_items', 0),
      ('database_version', 1)
    `);

    // Migration record
    await pool.query(`
      INSERT INTO migrations (name) VALUES ('001_comprehensive_schema_postgres')
    `);

    logger.info('✅ All tables, indexes, and initial data created successfully!');
    
  } catch (error: any) {
    logger.error('❌ Database reset failed:', error);
    throw error;
  } finally {
    if (pool) {
      await pool.end();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run reset if this script is executed directly
if (import.meta.main) {
  resetDatabase()
    .then(() => {
      logger.info('✨ Database reset completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Database reset failed:', error);
      process.exit(1);
    });
}

export { resetDatabase };
