-- Comprehensive SQLite schema for DOAXVV Handbook
-- Migration 001: Complete schema with all entities

-- Drop existing tables if they exist (for fresh start)
DROP TABLE IF EXISTS activity_logs;
DROP TABLE IF EXISTS user_statistics;
DROP TABLE IF EXISTS user_settings;
DROP TABLE IF EXISTS shop_items;
DROP TABLE IF EXISTS memories;
DROP TABLE IF EXISTS documents;
DROP TABLE IF EXISTS document_categories;
DROP TABLE IF EXISTS bromides;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS venus_boards;
DROP TABLE IF EXISTS girl_accessories;
DROP TABLE IF EXISTS swimsuit_skills;
DROP TABLE IF EXISTS accessories;
DROP TABLE IF EXISTS girls;
DROP TABLE IF EXISTS swimsuits;
DROP TABLE IF EXISTS skills;
DROP TABLE IF EXISTS characters;
DROP TABLE IF EXISTS migrations;

-- Characters table (base characters in the game)
CREATE TABLE characters (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_jp TEXT,
  name_en TEXT,
  name_zh TEXT,
  rarity TEXT CHECK (rarity IN ('SSR', 'SR', 'R', 'N')) DEFAULT 'R',
  birthday DATE,
  height INTEGER CHECK (height > 0 AND height < 300),
  bust INTEGER CHECK (bust > 0 AND bust < 200),
  waist INTEGER CHECK (waist > 0 AND waist < 200),
  hip INTEGER CHECK (hip > 0 AND hip < 200),
  hobby TEXT,
  favorite_food TEXT,
  description TEXT,
  avatar_image TEXT,
  background_image TEXT,
  voice_actor TEXT,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Skills table
CREATE TABLE skills (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('offensive', 'defensive', 'support', 'special', 'technical', 'balanced', 'appeal')),
  category TEXT DEFAULT 'general',
  description TEXT,
  icon TEXT,
  max_level INTEGER DEFAULT 1 CHECK (max_level > 0 AND max_level <= 10),
  effect_type TEXT,
  effect_value REAL,
  cooldown INTEGER DEFAULT 0,
  duration INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Swimsuits table
CREATE TABLE swimsuits (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  character_id TEXT NOT NULL,
  rarity TEXT NOT NULL CHECK (rarity IN ('UR', 'SSR', 'SR', 'R', 'N')),
  type TEXT DEFAULT 'swimsuit' CHECK (type IN ('swimsuit', 'outfit', 'dress', 'casual')),
  pow INTEGER NOT NULL CHECK (pow >= 0),
  tec INTEGER NOT NULL CHECK (tec >= 0),
  stm INTEGER NOT NULL CHECK (stm >= 0),
  apl INTEGER NOT NULL CHECK (apl >= 0),
  release_date DATE NOT NULL,
  reappear_date DATE,
  image TEXT,
  thumbnail_image TEXT,
  gacha_type TEXT DEFAULT 'standard',
  is_limited INTEGER DEFAULT 0,
  is_collab INTEGER DEFAULT 0,
  collab_series TEXT,
  trend_type TEXT,
  cost INTEGER DEFAULT 0,
  upgrade_cost INTEGER DEFAULT 0,
  max_level INTEGER DEFAULT 80 CHECK (max_level > 0),
  description TEXT,
  obtain_method TEXT DEFAULT 'gacha',
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Girls table (User's collection)
CREATE TABLE girls (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  character_id TEXT,
  type TEXT NOT NULL CHECK (type IN ('pow', 'tec', 'stm', 'apl')),
  level INTEGER NOT NULL CHECK (level >= 1 AND level <= 100),
  experience INTEGER DEFAULT 0 CHECK (experience >= 0),
  pow INTEGER NOT NULL CHECK (pow >= 0),
  tec INTEGER NOT NULL CHECK (tec >= 0),
  stm INTEGER NOT NULL CHECK (stm >= 0),
  apl INTEGER NOT NULL CHECK (apl >= 0),
  max_pow INTEGER NOT NULL CHECK (max_pow >= pow),
  max_tec INTEGER NOT NULL CHECK (max_tec >= tec),
  max_stm INTEGER NOT NULL CHECK (max_stm >= stm),
  max_apl INTEGER NOT NULL CHECK (max_apl >= apl),
  potential_pow INTEGER DEFAULT 0 CHECK (potential_pow >= 0),
  potential_tec INTEGER DEFAULT 0 CHECK (potential_tec >= 0),
  potential_stm INTEGER DEFAULT 0 CHECK (potential_stm >= 0),
  potential_apl INTEGER DEFAULT 0 CHECK (potential_apl >= 0),
  birthday DATE NOT NULL,
  swimsuit_id TEXT,
  is_awakened INTEGER DEFAULT 0,
  awakening_level INTEGER DEFAULT 0 CHECK (awakening_level >= 0 AND awakening_level <= 5),
  friendship_level INTEGER DEFAULT 1 CHECK (friendship_level >= 1 AND friendship_level <= 30),
  mood TEXT DEFAULT 'normal' CHECK (mood IN ('excellent', 'good', 'normal', 'tired', 'exhausted')),
  last_activity DATETIME DEFAULT (datetime('now')),
  notes TEXT,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Accessories table
CREATE TABLE accessories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('head', 'face', 'hand', 'body', 'back', 'special')),
  rarity TEXT DEFAULT 'R' CHECK (rarity IN ('UR', 'SSR', 'SR', 'R', 'N')),
  skill_id TEXT NOT NULL,
  pow INTEGER DEFAULT 0 CHECK (pow >= 0),
  tec INTEGER DEFAULT 0 CHECK (tec >= 0),
  stm INTEGER DEFAULT 0 CHECK (stm >= 0),
  apl INTEGER DEFAULT 0 CHECK (apl >= 0),
  level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 40),
  max_level INTEGER DEFAULT 40 CHECK (max_level >= level),
  image TEXT,
  obtain_method TEXT DEFAULT 'shop',
  cost INTEGER DEFAULT 0,
  description TEXT,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Swimsuit Skills junction table
CREATE TABLE swimsuit_skills (
  swimsuit_id TEXT NOT NULL,
  skill_id TEXT NOT NULL,
  position INTEGER NOT NULL CHECK (position >= 1 AND position <= 4),
  skill_level INTEGER DEFAULT 1 CHECK (skill_level >= 1),
  is_awakened INTEGER DEFAULT 0,
  PRIMARY KEY (swimsuit_id, skill_id, position)
);

-- Girl Accessories junction table
CREATE TABLE girl_accessories (
  girl_id TEXT NOT NULL,
  accessory_id TEXT NOT NULL,
  slot TEXT NOT NULL CHECK (slot IN ('head', 'face', 'hand', 'body')),
  equipped_at DATETIME DEFAULT (datetime('now')),
  PRIMARY KEY (girl_id, slot),
  UNIQUE (girl_id, accessory_id)
);

-- Venus Boards table
CREATE TABLE venus_boards (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  girl_id TEXT NOT NULL,
  board_type TEXT DEFAULT 'standard' CHECK (board_type IN ('standard', 'premium', 'event')),
  pow INTEGER NOT NULL CHECK (pow >= 0),
  tec INTEGER NOT NULL CHECK (tec >= 0),
  stm INTEGER NOT NULL CHECK (stm >= 0),
  apl INTEGER NOT NULL CHECK (apl >= 0),
  nodes_unlocked INTEGER DEFAULT 0 CHECK (nodes_unlocked >= 0),
  total_nodes INTEGER DEFAULT 25 CHECK (total_nodes > 0),
  completion_percentage REAL DEFAULT 0.00 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  is_completed INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Events table
CREATE TABLE events (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('festival', 'gacha', 'ranking', 'mission', 'collab')),
  description TEXT,
  start_date DATETIME NOT NULL,
  end_date DATETIME NOT NULL,
  image TEXT,
  is_active INTEGER DEFAULT 0,
  rewards TEXT, -- Store reward information as JSON
  requirements TEXT, -- Store requirement information as JSON
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now')),
  CHECK (end_date > start_date)
);

-- Bromides table
CREATE TABLE bromides (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect')),
  rarity TEXT NOT NULL CHECK (rarity IN ('N', 'R', 'SR', 'SSR', 'UR')),
  description TEXT,
  character_id TEXT,
  effects TEXT, -- Store effects as JSON
  source TEXT,
  image TEXT,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Document Categories table
CREATE TABLE document_categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT DEFAULT '#6B7280',
  description TEXT,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Documents table
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT, -- JSON array of tags
  author TEXT NOT NULL,
  is_published INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Memories table
CREATE TABLE memories (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('photo', 'video')),
  date DATE NOT NULL,
  characters TEXT, -- JSON array of character names/IDs
  tags TEXT, -- JSON array of tags
  thumbnail TEXT,
  favorite INTEGER DEFAULT 0,
  unlocked INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- Shop Items table
CREATE TABLE shop_items (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('swimsuit', 'accessory', 'decoration', 'currency', 'booster')),
  category TEXT NOT NULL,
  section TEXT NOT NULL CHECK (section IN ('owner', 'event', 'venus', 'vip')),
  price INTEGER NOT NULL CHECK (price >= 0),
  currency TEXT NOT NULL CHECK (currency IN ('coins', 'gems', 'tickets')),
  rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  image TEXT,
  in_stock INTEGER DEFAULT 1,
  is_new INTEGER DEFAULT 0,
  discount INTEGER DEFAULT 0 CHECK (discount >= 0 AND discount <= 100),
  limited_time INTEGER DEFAULT 0,
  featured INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- User Settings table
CREATE TABLE user_settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  data_type TEXT DEFAULT 'string' CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
  category TEXT DEFAULT 'general',
  description TEXT,
  is_public INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);

-- User Statistics table
CREATE TABLE user_statistics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  stat_name TEXT NOT NULL,
  stat_value INTEGER NOT NULL DEFAULT 0,
  last_updated DATETIME DEFAULT (datetime('now')),
  UNIQUE (stat_name)
);

-- Activity Log table for tracking user actions
CREATE TABLE activity_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  action_type TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  old_values TEXT,
  new_values TEXT,
  ip_address TEXT,
  user_agent TEXT,
  timestamp DATETIME DEFAULT (datetime('now'))
);

-- Migrations table for tracking schema changes
CREATE TABLE migrations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  executed_at DATETIME DEFAULT (datetime('now'))
);

-- Create indexes for better performance
CREATE INDEX idx_characters_name ON characters(name);
CREATE INDEX idx_characters_rarity ON characters(rarity);
CREATE INDEX idx_swimsuits_character_id ON swimsuits(character_id);
CREATE INDEX idx_swimsuits_rarity ON swimsuits(rarity);
CREATE INDEX idx_swimsuits_release_date ON swimsuits(release_date);
CREATE INDEX idx_swimsuits_type ON swimsuits(type);
CREATE INDEX idx_girls_character_id ON girls(character_id);
CREATE INDEX idx_girls_swimsuit_id ON girls(swimsuit_id);
CREATE INDEX idx_girls_level ON girls(level);
CREATE INDEX idx_girls_type ON girls(type);
CREATE INDEX idx_accessories_skill_id ON accessories(skill_id);
CREATE INDEX idx_accessories_type ON accessories(type);
CREATE INDEX idx_accessories_rarity ON accessories(rarity);
CREATE INDEX idx_swimsuit_skills_swimsuit_id ON swimsuit_skills(swimsuit_id);
CREATE INDEX idx_swimsuit_skills_skill_id ON swimsuit_skills(skill_id);
CREATE INDEX idx_girl_accessories_girl_id ON girl_accessories(girl_id);
CREATE INDEX idx_girl_accessories_accessory_id ON girl_accessories(accessory_id);
CREATE INDEX idx_venus_boards_girl_id ON venus_boards(girl_id);
CREATE INDEX idx_events_type ON events(type);
CREATE INDEX idx_events_active ON events(is_active);
CREATE INDEX idx_events_dates ON events(start_date, end_date);
CREATE INDEX idx_bromides_type ON bromides(type);
CREATE INDEX idx_bromides_rarity ON bromides(rarity);
CREATE INDEX idx_bromides_character_id ON bromides(character_id);
CREATE INDEX idx_documents_category ON documents(category);
CREATE INDEX idx_documents_published ON documents(is_published);
CREATE INDEX idx_documents_author ON documents(author);
CREATE INDEX idx_memories_type ON memories(type);
CREATE INDEX idx_memories_date ON memories(date);
CREATE INDEX idx_memories_favorite ON memories(favorite);
CREATE INDEX idx_shop_items_type ON shop_items(type);
CREATE INDEX idx_shop_items_section ON shop_items(section);
CREATE INDEX idx_shop_items_rarity ON shop_items(rarity);
CREATE INDEX idx_shop_items_featured ON shop_items(featured);
CREATE INDEX idx_shop_items_in_stock ON shop_items(in_stock);
CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp);
CREATE INDEX idx_activity_logs_entity ON activity_logs(entity_type, entity_id);

-- Insert default document categories
INSERT INTO document_categories (id, name, color, description) VALUES
  ('tutorial', 'Tutorial', '#3B82F6', 'Step-by-step guides and tutorials'),
  ('reference', 'Reference', '#10B981', 'Reference materials and documentation'),
  ('gameplay', 'Gameplay', '#F59E0B', 'Gameplay strategies and tips'),
  ('update', 'Updates', '#EF4444', 'Game updates and patch notes'),
  ('community', 'Community', '#8B5CF6', 'Community resources and guides');

-- Insert initial migration record
INSERT INTO migrations (name) VALUES ('001_comprehensive_schema_sqlite');

-- Initialize user statistics
INSERT INTO user_statistics (stat_name, stat_value) VALUES
  ('total_characters', 0),
  ('total_swimsuits', 0),
  ('total_skills', 0),
  ('total_girls', 0),
  ('total_accessories', 0),
  ('total_events', 0),
  ('total_bromides', 0),
  ('total_documents', 0),
  ('total_memories', 0),
  ('total_shop_items', 0),
  ('database_version', 1);
